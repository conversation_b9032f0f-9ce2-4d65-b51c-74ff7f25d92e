import React, { useEffect } from 'react';
import Cookies from 'js-cookie';
import { BrowserRouter as Router, Route, Routes, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import ProtectedRoute from './services/protectedRoute';
import Dashboard from './pages/dashboard/Dashboard';
import ForgetPassword from './pages/auth/ForgetPassword';
import PasswordReset from './pages/auth/PasswordReset';
import HospitalsPage from './pages/dashboard/HospitalsPage';
import UserPage from './pages/dashboard/UserPage';
import TargetPage from './pages/target/TargetPage';
import './assets/css/components/buttons.css'
import './assets/css/main/main.css'
import AccountPage from './pages/account/AccountPage';
import PositionPage from './pages/dashboard/PositionPage';
import MeasurePage from './pages/dashboard/measures/MeasurePage';
import MeasureDataPage from './pages/dashboard/measures/MeasureDataPage';
import { systemState } from './api';
import UnderMaintenance from './pages/communication/UnderMaintenance';
import LoginPage from './pages/auth/LoginPage';
import ProfilePage from './pages/profile/ProfilePage';
import SingleHospitalPage from './pages/singleHospital/singleHospitalPage';
import Notifications from './pages/dashboard/Notifications';
import SettingsPage from './pages/dashboard/SettingsPage';
import ReportsPage from './pages/dashboard/ReportsPage';
import UserRolesPage from './pages/dashboard/UserRolesPage';
import Hospitals from './pages/dashboard/Hospitals';
import UserDetailsPage from './pages/dashboard/UserDetails';
// import { useIsAuthenticated } from '@azure/msal-react';
import { MsalProvider } from '@azure/msal-react';
import { PublicClientApplication } from '@azure/msal-browser';
import { msalConfig } from './components/forms/msalConfig';
import PasswordResetFrom from './components/forms/PasswordResetForm';
import ErrorPage from './components/ErrorComponent/ErrorPage';
import useNetworkStatus from './components/ErrorComponent/useNetworkStatus';
import { hasRole } from './services/userPosition';
import TestConfirmPopup from './components/forms/TestConfirmPopup';
import TestPopupPage from './pages/TestPopupPage';
import TestBulkMeasureData from './pages/TestBulkMeasureData';

function App() {
  const isOnline = useNetworkStatus();
  // const isAuthenticated = useIsAuthenticated();
  const msalInstance = new PublicClientApplication(msalConfig);


  useEffect(() => {


    if (isOnline){


    const handleShortcut = (event) => {
      if (event.ctrlKey || event.altKey) {
        switch (event.key) {
          case 'h':
            event.preventDefault();
            window.location.href = '/';
            break;
          case 'q':
            event.preventDefault();
            window.location.href = '/account/';
            break;
            case 'm':
              event.preventDefault();
            window.location.href = '/measures/';
            break;
          default:

        }
      }
    };

    window.addEventListener('keydown', handleShortcut);
    return () => {
      window.removeEventListener('keydown', handleShortcut);
    };
  }
  }, [isOnline]);
  if (!isOnline){

    return <ErrorPage errorCode="offline" />
  }
  const canAccessSystemUnderMaintenance = Cookies.get('canAccessSystemUnderMaintenance');

  return (


    <MsalProvider instance={msalInstance}>
      <>

        <Router>
          {
            systemState === 'maintenancel' && !canAccessSystemUnderMaintenance
              ? <Routes>
                <Route path="*" element={<UnderMaintenance />} />
              </Routes>
              :
              <Routes>

                <Route
                  path="/"
                  element={
                    hasRole(["Manager","User","User Editor"])

                      ? <Navigate to={`/hospitals/${localStorage.getItem("hospital")}/${localStorage.getItem("hospital_id")}/`} replace />
                      : <ProtectedRoute><Dashboard /></ProtectedRoute>
                  }
                />
                <Route path="/hospitals/" element={<ProtectedRoute><HospitalsPage /></ProtectedRoute>} />
                <Route path="/all_hospitals/" element={<ProtectedRoute><Hospitals /></ProtectedRoute>} />
                <Route path="/users-list" element={<ProtectedRoute><UserPage /></ProtectedRoute>} />
                <Route path='/forgot-password/' element={<ForgetPassword />} />
                <Route path='/reset-password/' element={<PasswordResetFrom />} />
                <Route path="/positions/" element={<ProtectedRoute><PositionPage /></ProtectedRoute>} />
                <Route path="/measures/" element={<ProtectedRoute><MeasurePage /></ProtectedRoute>} />
                <Route path="/measure_data/" element={<ProtectedRoute><MeasureDataPage /></ProtectedRoute>} />
                <Route path="/notifications/" element={<ProtectedRoute><Notifications /></ProtectedRoute>} />
                <Route path="/settings/" element={<ProtectedRoute><SettingsPage /></ProtectedRoute>} />
                <Route path="/reports/" element={<ProtectedRoute><ReportsPage /></ProtectedRoute>} />
                <Route path="/users/" element={<ProtectedRoute><UserPage /></ProtectedRoute>} />
                <Route path="/user-roles" element={<UserRolesPage />} />
                <Route path="/login/" element={<LoginPage />} />
                <Route path="/hospitalscopy/" element={<Hospitals />} />
                <Route path="/test-popup-direct/" element={<TestConfirmPopup />} />
                <Route path="/test-popup-page/" element={<TestPopupPage />} />
                <Route path="/test-bulk-measure/" element={<TestBulkMeasureData />} />
                <Route path="/targets/" element={<ProtectedRoute><TargetPage /></ProtectedRoute>} />
                <Route path='/forgot-password/' element={<ForgetPassword />} />
                <Route path='/reset-password/' element={<PasswordReset />} />

                <Route path='/hospitals/:hospitalName/:hospitalId/' element={<ProtectedRoute><SingleHospitalPage /></ProtectedRoute>} />
                <Route path='/account/' element={<AccountPage />} />
                <Route path="/users/:userId" element={<UserDetailsPage />} />
                <Route path='/profilepage/' element={<ProtectedRoute><ProfilePage /></ProtectedRoute>} />
                <Route path='/test-popup/' element={<ProtectedRoute><TestConfirmPopup /></ProtectedRoute>} />

                <Route
                    path="/403"
                    element={<ErrorPage errorCode="403" message="You don't have permission to access this page." />}
                />
                <Route
                    path="/404"
                    element={<ErrorPage errorCode="404" message="The page you're looking for doesn't exist." />}
                />
                <Route
                    path="/500"
                    element={<ErrorPage errorCode="500" message="Internal Server Error. Please try again later." />}
                />
                <Route
                    path="/409"
                    element={<ErrorPage errorCode="409" message="Conflict: There is a data issue. Please check again." />}
                />

                <Route
                    path="offline"
                    element={<ErrorPage errorCode="offline" message="It seems you're offline. Check your connection and try again." />}
                />
                <Route path="*" element={<ErrorPage errorCode="404" message="The page you're looking for doesn't exist." />} />


              </Routes>

          }

        </Router>


        <Toaster />
        {/* </PermissionsProvider> */}
      </>
    </MsalProvider>

  );
}


export default App;
