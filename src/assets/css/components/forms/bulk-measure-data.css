/* Bulk Measure Data Form Styles */
.bulk-measure-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 20px;
}

.bulk-measure-form {
    background: white;
    border-radius: 12px;
    padding: 30px;
    width: 90%;
    max-width: 1200px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.bulk-measure-form h2 {
    margin: 0 0 25px 0;
    color: #333;
    font-size: 24px;
    font-weight: 600;
    text-align: center;
}

/* Date Range Section */
.date-range-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.date-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 15px;
}

.date-field label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.date-error {
    color: #dc3545;
    font-size: 14px;
    margin: 10px 0 0 0;
    padding: 8px 12px;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
}

/* Bulk Entries Section */
.bulk-entries-section {
    margin-top: 20px;
}

.entries-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.entries-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

/* Table Styles */
.entries-table {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 25px;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 2fr 1.5fr 120px;
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

.header-cell {
    padding: 15px 12px;
    font-weight: 600;
    color: #495057;
    border-right: 1px solid #dee2e6;
    text-align: center;
}

.header-cell:last-child {
    border-right: none;
}

.table-body {
    max-height: 400px;
    overflow-y: auto;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 2fr 1.5fr 120px;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.2s ease;
}

.table-row:hover {
    background-color: #f8f9fa;
}

.table-row:last-child {
    border-bottom: none;
}

.table-cell {
    padding: 12px;
    border-right: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
}

.table-cell:last-child {
    border-right: none;
}

/* Form Controls */
.measure-select,
.hospital-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    background: white;
    color: #495057;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.measure-select:focus,
.hospital-select:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.value-input-container {
    display: flex;
    align-items: center;
    width: 100%;
    position: relative;
}

.value-input {
    width: 100%;
    padding: 8px 30px 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.value-input:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.unit-symbol {
    position: absolute;
    right: 12px;
    color: #6c757d;
    font-weight: 500;
    pointer-events: none;
}

.action-buttons-cell {
    display: flex;
    gap: 5px;
    align-items: center;
    justify-content: center;
}

.duplicate-btn {
    background: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.duplicate-btn:hover {
    background: #218838;
}

.remove-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.remove-btn:hover {
    background: #c82333;
}

.remove-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
}

/* Messages */
.no-data-message,
.instruction-message {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    font-size: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 20px 0;
}

.no-data-message {
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .bulk-measure-overlay {
        padding: 10px;
    }
    
    .bulk-measure-form {
        width: 95%;
        padding: 20px;
        max-height: 95vh;
    }
    
    .date-inputs {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .table-header,
    .table-row {
        grid-template-columns: 1fr;
        gap: 1px;
    }
    
    .table-cell {
        padding: 8px;
        border-right: none;
        border-bottom: 1px solid #dee2e6;
    }
    
    .table-cell:last-child {
        border-bottom: none;
    }
    
    .header-cell {
        padding: 10px 8px;
        border-right: none;
        border-bottom: 1px solid #dee2e6;
    }
    
    .header-cell:last-child {
        border-bottom: none;
    }
    
    .form-actions {
        flex-direction: column;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .bulk-measure-form h2 {
        font-size: 20px;
    }

    .entries-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .entries-header h3 {
        text-align: center;
    }
}

/* Action buttons styling for the measure data page */
.action-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        gap: 8px;
        width: 100%;
    }
}
