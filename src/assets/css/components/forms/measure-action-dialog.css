.measure-action-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.measure-action-dialog {
  background: white;
  border-radius: 12px;
  padding: 30px;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  animation: slideIn 0.3s ease-out;
}
.measure-action-dialog h2 {
  color: #333;
  font-size: 24px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 10px;
}
.measure-action-dialog > p {
  color: #666;
  text-align: center;
  font-size: 16px;
  margin-bottom: 25px;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.action-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 25px;
}

.action-option {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.2s ease;
}
.action-option:hover {
  border-color: #07AEEF;
  background-color: #f8f9fa;
}

.option-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.option-icon.create-icon {
  background-color: #e8f5e8;
  color: #28a745;
}
.option-icon.copy-icon {
  background-color: #e3f2fd;
  color: #1976d2;
}

.option-content {
  flex: 1;
}
.option-content h3 {
  color: #333;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
}
.option-content p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.dialog-actions {
  display: flex;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

@media (max-width: 600px) {
  .measure-action-dialog {
    padding: 20px;
    margin: 20px;
  }
  .action-option {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  .option-content {
    text-align: center;
  }
}/*# sourceMappingURL=measure-action-dialog.css.map */