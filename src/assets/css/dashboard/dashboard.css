* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.total-hospital-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  -moz-column-gap: 20px;
       column-gap: 20px;
}
.total-hospital-cards .icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
}
.total-hospital-cards #acute .icon-container {
  background-color: #f7fdff;
  padding: 10px;
  border-radius: 50%;
}
.total-hospital-cards #swing-bed .icon-container {
  background-color: #fffdf7;
  padding: 10px;
  border-radius: 50%;
}
.total-hospital-cards #observation .icon-container {
  background-color: #fff3f2;
  padding: 10px;
  border-radius: 50%;
}
.total-hospital-cards #emergency .icon-container {
  background-color: #f4fbff;
  padding: 10px;
  border-radius: 50%;
}
.total-hospital-cards .total-card {
  display: flex;
  gap: 20px;
  align-items: center;
  border: 1px solid #e5e5e5;
  padding: 16px;
  border-radius: 8px;
}
.total-hospital-cards .total-card .col {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.total-hospital-cards .total-card .col p {
  color: #999999;
  font-size: 14px;
}

.dashboard-container,
.hospitals-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  background-color: white;
  padding: 24px;
  border-radius: 0.5rem;
}
.dashboard-container .top-actions,
.hospitals-container .top-actions {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  align-items: center;
  gap: 20px;
}
.dashboard-container .top-actions .filter-container,
.hospitals-container .top-actions .filter-container {
  display: flex;
  gap: 12px;
}
.dashboard-container .top-actions .hospital-year,
.hospitals-container .top-actions .hospital-year {
  display: flex;
  gap: 10px;
}
.dashboard-container .top-actions .export-bttn,
.hospitals-container .top-actions .export-bttn {
  background-color: aliceblue;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  height: -moz-fit-content;
  height: fit-content;
  border-color: transparent;
  font-size: inherit;
  border-radius: 0.4rem;
  text-wrap: nowrap;
  cursor: pointer;
}
.dashboard-container .top-actions .export-icon,
.hospitals-container .top-actions .export-icon {
  display: flex;
  gap: 5px;
  align-items: center;
  cursor: pointer;
}
.dashboard-container .top-actions select,
.hospitals-container .top-actions select {
  padding: 25px 10px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
}
.dashboard-container .top-actions .yearly-dates,
.hospitals-container .top-actions .yearly-dates {
  display: flex;
  gap: 20px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  padding: 10px;
}
.dashboard-container .top-actions .yearly-dates .yearly-col,
.hospitals-container .top-actions .yearly-dates .yearly-col {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
.dashboard-container .chart-row,
.hospitals-container .chart-row {
  width: 100%;
  display: grid;
  grid-template-columns: 65% 35%;
  gap: 20px;
}
.dashboard-container .chart-row .chart-container,
.hospitals-container .chart-row .chart-container {
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  padding: 20px;
}
.dashboard-container .chart-row .chart-container .row,
.hospitals-container .chart-row .chart-container .row {
  display: flex;
  gap: 10px;
  align-items: center;
}
.dashboard-container .chart-row .chart-container .chart,
.hospitals-container .chart-row .chart-container .chart {
  width: 100%;
}
.dashboard-container .chart-row .progress-container,
.hospitals-container .chart-row .progress-container {
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.dashboard-container .chart-row .progress-container > .row,
.hospitals-container .chart-row .progress-container > .row {
  display: flex;
  flex-direction: column;
  gap: 20px;
  justify-content: space-between;
}
.dashboard-container .chart-row .progress-container > .row h4,
.hospitals-container .chart-row .progress-container > .row h4 {
  font-size: 18px;
}
.dashboard-container .chart-row .progress-container > .row .description,
.hospitals-container .chart-row .progress-container > .row .description {
  background-color: #07AEEF;
  font-size: 16px;
  color: white;
  padding: 10px;
  border-radius: 10px;
}
.dashboard-container .chart-row .progress-container .hospital-progress,
.hospitals-container .chart-row .progress-container .hospital-progress {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.dashboard-container .chart-row .progress-container .hospital-progress > .row,
.hospitals-container .chart-row .progress-container .hospital-progress > .row {
  display: flex;
  gap: 10px;
  justify-content: space-between;
}
.dashboard-container .chart-row .progress-container .hospital-progress > .row .name,
.hospitals-container .chart-row .progress-container .hospital-progress > .row .name {
  max-width: 50px;
}
.dashboard-container .table-container,
.hospitals-container .table-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.dashboard-container .table-container .row,
.hospitals-container .table-container .row {
  display: flex;
  gap: 20px;
  align-items: center;
}
.dashboard-container .table-container table,
.hospitals-container .table-container table {
  border-collapse: collapse;
  width: 100%;
}
.dashboard-container .table-container table tr td,
.dashboard-container .table-container table tr th,
.hospitals-container .table-container table tr td,
.hospitals-container .table-container table tr th {
  border: 1px solid #ccc;
  padding: 10px 20px;
  text-align: start;
}
.dashboard-container .table-container table tr .bm,
.hospitals-container .table-container table tr .bm {
  background-color: #44536a;
  color: #ffffff;
}

.green-bg {
  background-color: #c6efcd;
  color: #016100;
}

.red-bg {
  background-color: #ffc7cd;
  color: #9c0007;
}

.yellow-bg {
  background-color: #ffeb9c;
  color: #9c5700;
}

@media screen and (max-width: 1080px) {
  .total-hospital-cards {
    grid-template-columns: repeat(2, 1fr);
    row-gap: 20px;
  }
  .dashboard-page .sidebar {
    display: none;
  }
}
@media screen and (max-width: 1040px) {
  .dashboard-container .chart-row {
    grid-template-columns: 100%;
  }
}
@media screen and (max-width: 860px) {
  .dashboard-container .chart-row {
    flex-direction: column;
  }
  .dashboard-container .chart-row .chart-container {
    width: 100%;
  }
  .dashboard-container .table-container {
    overflow-x: auto;
  }
}
@media screen and (max-width: 720px) {
  .dashboard-container .top-actions {
    gap: 12px;
  }
}
@media screen and (max-width: 600px) {
  .dashboard-container .top-actions {
    flex-direction: column;
  }
  .dashboard-container .top-actions .filter-container {
    display: flex;
    gap: 12px;
  }
  .dashboard-container .top-actions .export-bttn {
    padding: 0.8rem 0.5rem;
  }
  .dashboard-container .chart-row .chart-container {
    padding-inline: 0px;
  }
  .dashboard-container .chart-row .chart-container .row {
    padding-inline: 20px;
  }
  .hospitals-container .top-actions {
    flex-direction: column;
  }
  .single-hospitals-page .main-container .hospitals-container .charts .pie-chart-container .pie-chart {
    display: none;
  }
  .total-hospital-cards {
    grid-template-columns: repeat(1, 1fr);
  }
}
.hover-container {
  position: relative;
  display: flex;
  justify-content: space-between;
}
.hover-container .hover-description {
  display: none;
  background-color: #07aeef;
  padding: 10px 20px;
  border-radius: 5px;
  width: -moz-fit-content;
  width: fit-content;
  color: white;
  z-index: 10;
  right: 0;
}
.hover-container .hover-info:hover .hover-description {
  position: absolute;
  display: block;
}/*# sourceMappingURL=dashboard.css.map */