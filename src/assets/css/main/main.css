* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.date-range-display {
  margin-top: 10px;
  font-weight: bold;
  color: #333;
}

.text-area {
  border: 1px solid blue;
}

.date-range {
  display: flex;
  gap: 10px;
}
.date-range .date {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.values {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.values label {
  color: gray;
}
.values .label-text {
  display: flex;
  gap: 10px;
  align-items: center;
}
.values .symbols {
  border: 1px solid #e8e7e7;
  padding: 10px;
  color: gray;
  border-radius: 0.4rem;
}

a {
  text-decoration: none;
  color: inherit;
}

.table-container {
  overflow-x: auto;
}

table {
  border-collapse: collapse;
  width: 100%;
}
table tr td,
table tr th {
  border: 1px solid #ccc;
  padding: 10px 20px;
  text-align: start;
}
table tr .td-img {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 26px 20px;
  border: 0.5px solid #ccc;
}
table tr .td-img img {
  border: 1px solid #ccc;
}
table tr .bm {
  background-color: #44536a;
  color: #ffffff;
}
table .draggable {
  background: #571616;
  border: 1px solid #7f5151;
  cursor: grab;
}
table .draggable:hover {
  background: #df5656;
}
table .droppable {
  background: #612626;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

/* Action buttons improvements - Global styles */
.action-buttons {
  display: flex;
  gap: 15px;
  align-items: center;
  justify-content: center;
}

.action-buttons svg {
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.action-buttons svg:hover {
  background-color: #f8f9fa;
  transform: scale(1.1);
}

.action-buttons .edit-icon {
  color: #28a745;
}

.action-buttons .edit-icon:hover {
  background-color: #d4edda;
  color: #155724;
}

.action-buttons .delete-icon {
  color: #dc3545;
}

.action-buttons .delete-icon:hover {
  background-color: #f8d7da;
  color: #721c24;
}

.popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}
.popup .popup-content {
  background-color: white;
  padding: 2rem;
  border-radius: 1rem;
}

.maintenance-page {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.maintenance-page .container {
  background-color: #FBFEFF;
  border: 6px solid #F6FCFF;
  padding: 5rem 9rem;
  display: grid;
  align-content: center;
  justify-items: center;
  gap: 2rem;
  width: 60vw;
  border-radius: 40px;
}
.maintenance-page .container .logo {
  max-width: 100px;
}
.maintenance-page .container .shape {
  width: 100%;
}
.maintenance-page .container h1 {
  color: #07AEEF;
}

.popup-buttons {
  display: flex;
  gap: 24px;
}

@media screen and (max-width: 840px) {
  .maintenance-page {
    padding: 2rem;
  }
  .maintenance-page .container {
    width: unset;
    max-width: 600px;
    padding: 2rem;
    border-radius: 1rem;
  }
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}/*# sourceMappingURL=main.css.map */