.confirm-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.confirm-popup.show {
  opacity: 1;
  pointer-events: auto;
}

.popup-content {
  background-color: #fff;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  min-width: 300px;
}

.popup-buttons {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 10px;
}

/* Measure Instructions */
.measure-instructions {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.measure-instructions h3 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
}

.measure-instructions ul {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
}

.measure-instructions li {
  margin-bottom: 5px;
  font-size: 14px;
}

/*# sourceMappingURL=popupmeasure.css.map */