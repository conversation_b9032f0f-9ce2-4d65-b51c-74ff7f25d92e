.confirm-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.confirm-popup.show {
  opacity: 1;
  pointer-events: auto;
}

.popup-content {
  background-color: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  min-width: 400px;
  max-width: 90%;
  text-align: center;
}

.popup-content h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.popup-content p {
  margin-bottom: 25px;
  font-size: 16px;
  line-height: 1.5;
  color: #555;
}

.popup-buttons {
  margin-top: 25px;
  display: flex;
  justify-content: center;
  gap: 15px;
}/*# sourceMappingURL=popupmeasure.css.map */