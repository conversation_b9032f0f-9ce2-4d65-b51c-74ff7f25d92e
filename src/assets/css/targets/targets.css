.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}
.popup-overlay .add-target-popup {
  width: 50%;
  background-color: white;
  padding: 24px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: auto;
}
.popup-overlay .add-target-popup .title {
  color: black;
}
.popup-overlay .add-target-popup .dateInput {
  display: flex;
  gap: 10px;
}
.popup-overlay .add-target-popup .action-btn {
  display: flex;
  gap: 10px;
}
.popup-overlay .add-target-popup .label-dates {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.target-page .main-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.target-page .main-container .targets {
  background-color: white;
  padding: 24px;
  border-radius: 0.5rem;
  overflow: auto;
}
.target-page .main-container .targets .expanded-row {
  display: flex;
  gap: 24px;
}
.target-page .main-container .targets .expanded-row .dates {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.target-page .main-container .targets .expanded-row .dates .icon-date {
  display: flex;
  align-items: center;
  gap: 4px;
}
.target-page .main-container .buttons-search {
  display: flex;
  justify-content: space-between;
  background-color: white;
  padding: 24px;
  align-items: center;
  border-radius: 0.5rem;
}
.target-page .main-container .buttons-search .search-input {
  width: 50%;
}
.target-page .main-container .search-button {
  width: 100%;
  display: flex;
  gap: 12px;
}

@media screen and (max-width: 600px) {
  .target-page .main-container .search-button {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  .target-page .main-container .buttons-search {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  .target-page .main-container .buttons-search .search-input {
    width: 100%;
  }
}

/* Info tooltip styles */
.field-with-info {
  margin-bottom: 20px;
}

.field-label-with-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.field-label-with-info label {
  font-weight: 500;
  color: #333;
  margin: 0;
}

.info-tooltip {
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: help;
}

.info-tooltip:hover .tooltip-content {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.tooltip-content {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-10px);
  background: #333;
  color: white;
  padding: 12px;
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.4;
  width: 300px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tooltip-content::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: #333;
}

.tooltip-content strong {
  display: block;
  margin-bottom: 8px;
  color: #fff;
}

.tooltip-content ul {
  margin: 0;
  padding-left: 16px;
}

.tooltip-content li {
  margin-bottom: 4px;
}

.tooltip-content li:last-child {
  margin-bottom: 0;
}

/*# sourceMappingURL=targets.css.map */