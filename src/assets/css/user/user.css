@import url("https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300..900;1,300..900&display=swap");
input {
  background-color: transparent;
}
input:focus {
  background-color: transparent !important;
}

.css-11u53oe-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input.css-11u53oe-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input.css-11u53oe-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input {
  color: gray;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  padding: 14px;
  border: 1px solid #fefefe;
}

.field {
  display: grid;
  height: -moz-fit-content;
  height: fit-content;
  gap: 0.2rem;
  position: relative;
}
.field .input {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: 1px solid #eee;
  padding: 0.8rem;
  color: gray;
  border-radius: 0.4rem;
  transition: all 0.3s ease;
}
.field .active {
  border-color: #07AEEF;
}
.field input {
  width: 100%;
  border: none;
  font-size: inherit;
  outline: none;
}
.field input:focus {
  outline: none;
}
.field input::-moz-placeholder {
  color: gray;
}
.field input::placeholder {
  color: gray;
}
.field .date-input {
  border: 0;
  padding: 0;
  width: 100% !important;
}
.field .date-input input {
  border: 0;
  padding: 0.8rem !important;
  width: 100% !important;
}
.field .options {
  position: absolute;
  top: 4.6rem;
  left: 0;
  right: 0;
  z-index: 2;
  background-color: white;
  padding: 1rem;
  border-radius: 0.8rem;
  border: 1px solid #eee;
  scroll-behavior: auto;
  cursor: pointer;
  width: -moz-fit-content;
  width: fit-content;
}
.field .options .option {
  padding: 0.8rem;
  border-bottom: 1px solid #eee;
}
.field .options .option:last-child {
  border-bottom: none;
}

form {
  display: grid;
  gap: 1rem;
  height: -moz-fit-content;
  height: fit-content;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
}

.names {
  display: flex;
  gap: 24px;
}

.form {
  display: grid;
  gap: 1rem;
  height: -moz-fit-content;
  height: fit-content;
  width: 100%;
}

.no-data {
  padding: 4px;
  color: red;
  text-align: center;
  font-size: 14px;
}

.option {
  padding: 4px;
  cursor: pointer;
}

.option:hover {
  background-color: #f0f0f0;
}

.form-link {
  color: #07AEEF;
  text-decoration: underline;
}

.buttons {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.MuiFormControl-root.MuiTextField-root.css-z3c6am-MuiFormControl-root-MuiTextField-root {
  width: 100% !important;
}

.user-content {
  margin: 0;
  padding: 1rem;
  overflow: auto;
  top: 112px;
  left: 258px;
  font-family: "Rubik", sans-serif;
  border-radius: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.user-content .buttons-search {
  display: flex;
  background-color: white;
  justify-content: space-between;
  padding: 24px;
  border-radius: 0.4rem;
  align-items: center;
}
.user-content .buttons-search .add-user-bttns {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  gap: 24px;
}
.user-content .buttons-search .add-user-bttns .primary-btn {
  height: -moz-fit-content;
  height: fit-content;
}
.user-content .buttons-search .add-user-bttns .export-bttn {
  background-color: aliceblue;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 2rem;
  height: -moz-fit-content;
  height: fit-content;
  border-color: transparent;
  font-size: inherit;
  border-radius: 0.4rem;
  text-wrap: nowrap;
  cursor: pointer;
}
.user-content .buttons-search .search-users {
  width: 40%;
  padding-bottom: 1.25rem;
}
.user-content .tab {
  padding: 0.5rem 1rem;
  border-bottom: 2px solid transparent;
}
.user-content .tab.active {
  border-bottom: 2px solid #07AEEF;
  font-weight: bold;
}
.user-content .tabs {
  background-color: white;
  display: flex;
  gap: 12px;
  cursor: pointer;
  border-radius: 0.4rem;
  padding-top: 24px;
}
.user-content .table-container {
  background-color: white;
  border-radius: 0.4rem;
  padding: 24px;
}
.user-content .table-container tbody tr th {
  display: grid;
  grid-column: 1fr 1fr;
  box-sizing: border-box;
}
.user-content .table-container tbody tr td img {
  width: 1.625rem;
  height: 1.625rem;
  border-radius: 50%;
}
.user-content .table-container tbody tr td .more-userInfo {
  display: flex;
  flex-direction: row;
  gap: 1.25rem;
  padding-block: 0.5rem;
}
.user-content .table-container .pagination {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  padding-top: 0.75rem;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.498);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}
.overlay .popup-user {
  padding: 2rem;
  border-radius: 0.625rem;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
  max-width: 28.125rem;
  width: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  overflow-y: auto;
}
.overlay .popup-user .paragraph-green {
  background-color: #adf4ce;
  border-radius: 0.5rem;
  padding: 0.75rem;
  font-size: 0.9rem;
}
.overlay .popup-user .paragraph-red {
  background-color: #e38587;
  border-radius: 0.5rem;
  padding: 0.75rem;
  font-size: 0.9rem;
}
.overlay .popup-user form .form-step {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}
.overlay .popup-user form .form-step .inputs-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}
.overlay .popup-user form .form-step .popup-buttons {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.field .file-input {
  cursor: pointer;
  display: flex;
  flex-direction: row;
}
.field .file-input .icon-label {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.field .file-input .image-preview img {
  margin-left: 6.25rem;
}

.image-preview img {
  max-width: 2.75rem;
  max-height: 2.75rem;
  margin-top: 0.5rem;
  border-radius: 50%;
}

@media screen and (max-width: 600px) {
  .user-content {
    padding: 0;
  }
  .user-content .buttons-search {
    flex-direction: column;
  }
  .user-content .buttons-search .search-users {
    width: 100%;
  }
  .user-content .buttons-search .add-user-bttns {
    gap: 8px;
  }
}

/* Role filter container */
.role-filter-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reset-filter-btn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.reset-filter-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.reset-filter-btn svg {
  color: #6c757d;
}

/*# sourceMappingURL=user.css.map */