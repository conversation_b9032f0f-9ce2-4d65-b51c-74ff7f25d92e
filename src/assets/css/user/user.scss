@import url("https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300..900;1,300..900&display=swap");
@import "../variables.scss";
@import "../components/forms/forms.scss";
$sidebar-width: 258px;
$header-height: 112px;
$font: "Rubik", sans-serif;
$blue-color: #07aeef;
$grey-color: #4b4b4b;
$grey-light-color: #c9c9c9;

.user-content {
  margin: 0;
  padding: 1rem;
  overflow: auto;
  top: 112px;
  left: 258px;
  font-family: "Rubik", sans-serif;
  // background-color: white;
  border-radius: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 24px;

  .buttons-search {
    display: flex;
    background-color: white;
    justify-content: space-between;
    padding: 24px;
    border-radius: 0.4rem;
    align-items: center;

    .add-user-bttns {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      gap: 24px;

      .primary-btn {
        height: fit-content;
      }

      .export-bttn {
        background-color: aliceblue;
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 0.5rem;
        padding: 0.8rem 2rem;
        height: fit-content;
        border-color: transparent;
        font-size: inherit;
        border-radius: 0.4rem;
        text-wrap: nowrap;
        cursor: pointer;
      }
    }

    .search-users {
      width: 40%;
      padding-bottom: 1.25rem;
    }

  }


  .tab {
    padding: 0.5rem 1rem;
    border-bottom: 2px solid transparent;
  }

  .tab.active {
    border-bottom: 2px solid #07AEEF;
    font-weight: bold;
  }

  .tabs {
    background-color: white;
    display: flex;
    gap: 12px;
    cursor: pointer;
    border-radius: 0.4rem;
    padding-top: 24px;
  }

  .table-container {
    background-color: white;
    border-radius: 0.4rem;
    padding: 24px;

    tbody {
      tr {
        th {
          display: grid;
          grid-column: 1fr 1fr;
          box-sizing: border-box;
        }

        td {
          img {
            width: 1.625rem;
            height: 1.625rem;
            border-radius: 50%;
          }

          .more-userInfo {
            display: flex;
            flex-direction: row;
            gap: 1.25rem;
            padding-block: 0.5rem;
          }
        }
      }
    }

    .pagination {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      padding-top: 0.75rem;
    }
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.498);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;

  .popup-user {
    padding: 2rem;
    border-radius: 0.625rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
    max-width: 28.125rem;
    width: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    overflow-y: auto;

    .paragraph-green {
      background-color: #adf4ce;
      border-radius: 0.5rem;
      padding: 0.75rem;
      font-size: 0.9rem;
    }

    .paragraph-red {
      background-color: #e38587;
      border-radius: 0.5rem;
      padding: 0.75rem;
      font-size: 0.9rem;
    }

    form {
      .form-step {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;

        .inputs-row {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 0.75rem;
        }

        .popup-buttons {
          display: flex;
          gap: 0.75rem;
          justify-content: flex-end;
        }
      }
    }
  }
}

.field {
  .file-input {
    cursor: pointer;
    display: flex;
    flex-direction: row;

    .icon-label {
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    .image-preview img {
      margin-left: 6.25rem;
    }
  }
}

.image-preview img {
  max-width: 2.75rem;
  max-height: 2.75rem;
  margin-top: 0.5rem;
  border-radius: 50%;
}

@media screen and (max-width:600px) {
  .user-content{
    padding: 0;
    .buttons-search {
      flex-direction: column;
      .search-users{
        width: 100%;
      }
      .add-user-bttns{
        gap: 8px;
      }
    }
  }
  
}