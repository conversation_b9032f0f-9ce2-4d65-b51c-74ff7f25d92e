import { ArrowRight01Icon, ArrowDown01Icon, Menu01Icon, Notification02Icon, UserIcon, Logout01Icon } from 'hugeicons-react';
import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useParams } from 'react-router-dom';
import api, { API_URL } from '../../api';
import toast from 'react-hot-toast';
import Cookies from 'js-cookie';
import { hasRole } from '../../services/userPosition';

const DashboardHeader = ({ mobileMenuOpen, setMobileMenuOpen, pageTitle }) => {
    const [profileData, setProfileData] = useState({});
    const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
    const [userData, setUserData] = useState({});
    const [fullName, setFullName] = useState('');
    const [position, setPosition] = useState('');
    const [profileImage, setProfileImage] = useState('');
    const { userId } = useParams();
    const [isLoading, setIsLoading] = useState(true);
    const navigate = useNavigate();
    const [unreadCount, setUnreadCount] = useState(0);
    const path = window.location.pathname;
    const breadcrumbs = path.split('/').filter(Boolean);

    const breadcrumbLinks = breadcrumbs.map((breadcrumb, index) => {
        const to = '/' + breadcrumbs.slice(0, index + 1).join('/');
        
        let displayText = breadcrumb;
        if (breadcrumb.toLowerCase() === 'positions') {
            displayText = 'Roles & Permissions';
        }

        return (
            <span key={index} className='breadcrumb'>
                {index > 0 && <ArrowRight01Icon size={20} />}
                <Link to={to}>{displayText}</Link>
            </span>
        );
    });


    const fetchUnreadCount = async () => {
        try {
            const token = Cookies.get('accessToken');
            let unread = 0;
            let nextPage = `${API_URL}/notifications/`;

            while (nextPage) {
                const response = await api.get(nextPage);
                unread += response.data.results.filter(n => !n.is_read).length;
                nextPage = response.data.next;
            }

            setUnreadCount(unread);
        } catch (error) {
            console.error("Failed to fetch unread count", error);
        }
    };
    useEffect(() => {

        if (hasRole(['Admin', 'Super User'])) {
            fetchUnreadCount();
        }

    }, []);



    useEffect(() => {
        const fetchUserData = async () => {
            try {
                const token = Cookies.get('accessToken');
                const response = await api.get(`${API_URL}/user/profile/`

                );
                const user = response.data;
                setFullName(`${user.first_name} ${user.last_name}`);
                setPosition(user.position);
                setProfileImage(user.profile_image);
                setProfileData(user);
                setIsLoading(false);
            } catch (error) {
                console.error('Error fetching user data:', error);
                toast.error('Failed to load user data.');
                setIsLoading(false);
            }
        };

        fetchUserData();
    }, [userId]);

    const handleLogout = () => {
        Cookies.remove('accessToken');
        navigate('/login');
    };

    return (
        <div className='dashboard-header'>
            <div className="header-content">
                <div className="breadcrumbs">
                    <div className='menu-title'>
                        <Menu01Icon size={32} className='openMenu' onClick={() => setMobileMenuOpen(true)} />
                        <h4 className="page-title">{pageTitle}</h4>
                    </div>
                    <div className="breadcrumb-list">
                        <span className='breadcrumb'>
                            <Link to="/">Dashboard / </Link>
                        </span>
                        {breadcrumbLinks}
                    </div>
                </div>

                <div className="profile-and-links">

                    {
                        hasRole(['Admin', 'Super User']) && (
                            <div className="notification">
                                <Link to={`/notifications/`}>
                                    {unreadCount > 0 && (
                                        <>
                                            <div className="dot"></div>
                                            <span className="unread-count">{unreadCount}</span>
                                        </>
                                    )}
                                    <Notification02Icon />
                                </Link>
                            </div>

                        )
                    }

                    <div className="profile" onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}>
                        {profileData && profileData.image
                            ? <img src={profileData.image} alt={profileData.name} />
                            : <img src="/favicon.ico" alt="" className="logo" />
                        }
                        <ArrowDown01Icon size={24} color={"#000000"} variant={"stroke"} />
                    </div>

                    {isProfileMenuOpen && (
                        <>
                            <div className='profile-overlay' onClick={() => setIsProfileMenuOpen(false)}></div>

                            <div className='profile-drop-down'>
                                <div className="profile-name">
                                    <img src={profileImage || '/favicon.ico'} alt="" />
                                    <div className='name-position'>
                                        <p>{fullName}</p>
                                        <p>{position || 'N/A'} </p>
                                    </div>
                                </div>
                                <div className="profile-menu">
                                    <div className="profile-item">
                                        <UserIcon size={24} color={"#000000"} variant={"stroke"} />
                                        <Link to='/account/'>My Profile</Link>
                                    </div>
                                    <div className="profile-item" onClick={handleLogout}>
                                        <Logout01Icon size={24} color={"#000000"} variant={"stroke"} />
                                        <span>Logout</span>
                                    </div>
                                </div>
                            </div>
                        </>
                    )}


                </div>
            </div>
        </div>
    );
}

export default DashboardHeader;
