import React from 'react';

const ConfirmPopup = ({ message, onConfirm, onCancel, showConfirmPopup }) => {
    if (!showConfirmPopup) {  
        return null;
    }

    return (
        <div className="confirm-popup show"> 
            <div className="popup-content">
                <p>{message}</p>
                <div className="popup-buttons"> 
                    <button onClick={onConfirm}>Yes</button>
                    <button onClick={onCancel}>Cancel</button>
                </div>
            </div>
        </div>
    );
};

export default ConfirmPopup;