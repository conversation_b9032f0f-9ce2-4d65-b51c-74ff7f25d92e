import React from 'react';
import { PrimaryButton, SecondaryButton } from './buttons';

const ConfirmPopup = ({ message, onConfirm, onCancel, showConfirmPopup }) => {
    if (!showConfirmPopup) {
        return null;
    }

    return (
        <div className="confirm-popup show">
            <div className="popup-content">
                <h3>Confirm Measure ID</h3>
                <p>{message}</p>
                <div className="popup-buttons">
                    <SecondaryButton onClick={onCancel} buttonText="Cancel" />
                    <PrimaryButton onClick={onConfirm} buttonText="Yes, Use This ID" />
                </div>
            </div>
        </div>
    );
};

export default ConfirmPopup;