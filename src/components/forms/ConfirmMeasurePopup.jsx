import { useEffect } from 'react';
import { PrimaryButton, SecondaryButton } from './buttons';

const ConfirmPopup = ({ message, onConfirm, onCancel, showConfirmPopup }) => {
    useEffect(() => {
        console.log("ConfirmPopup rendered with showConfirmPopup:", showConfirmPopup);

        // Prevent scrolling of the background when popup is shown
        if (showConfirmPopup) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'auto';
        }

        return () => {
            document.body.style.overflow = 'auto';
        };
    }, [showConfirmPopup]);

    if (!showConfirmPopup) {
        return null;
    }

    return (
        <div className="confirm-popup show" style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 9999
        }}>
            <div className="popup-content" style={{
                backgroundColor: '#fff',
                padding: '30px',
                borderRadius: '8px',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.5)',
                minWidth: '400px',
                maxWidth: '90%',
                textAlign: 'center'
            }}>
                <h3 style={{ marginTop: 0, marginBottom: '15px', color: '#333', fontSize: '20px', fontWeight: 600 }}>
                    Confirm Measure ID
                </h3>
                <p style={{ marginBottom: '25px', fontSize: '16px', lineHeight: 1.5, color: '#555' }}>
                    {message}
                </p>
                <div className="popup-buttons" style={{
                    marginTop: '25px',
                    display: 'flex',
                    justifyContent: 'center',
                    gap: '15px'
                }}>
                    <SecondaryButton onClick={onCancel} buttonText="Cancel" />
                    <PrimaryButton onClick={onConfirm} buttonText="Yes, Use This ID" />
                </div>
            </div>
        </div>
    );
};

export default ConfirmPopup;