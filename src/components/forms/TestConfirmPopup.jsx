import React, { useState } from 'react';
import ConfirmPopup from './ConfirmMeasurePopup';
import { PrimaryButton } from './buttons';

const TestConfirmPopup = () => {
    const [showPopup, setShowPopup] = useState(false);
    
    const handleConfirm = () => {
        console.log('Confirmed!');
        setShowPopup(false);
    };
    
    const handleCancel = () => {
        console.log('Cancelled!');
        setShowPopup(false);
    };
    
    return (
        <div style={{ padding: '20px' }}>
            <h2>Test Confirm Popup</h2>
            <PrimaryButton 
                buttonText="Show Popup" 
                onClick={() => setShowPopup(true)} 
            />
            
            {showPopup && (
                <ConfirmPopup
                    message="This is a test message. Would you like to continue?"
                    onConfirm={handleConfirm}
                    onCancel={handleCancel}
                    showConfirmPopup={showPopup}
                />
            )}
        </div>
    );
};

export default TestConfirmPopup;
