import React, { useState, useRef, useEffect } from 'react';
import api from '../../../api';
import toast from 'react-hot-toast';
import '../../../assets/css/pages/measures/copyMeasuresModal.css';

const CopyMeasuresModal = ({ open, onClose, measures, currentYear }) => {
    const [selectedMeasures, setSelectedMeasures] = useState([]);
    const [targetYear, setTargetYear] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const modalRef = useRef(null);

    // Generate year options from current year + 1 to current year + 5
    const currentYearNum = new Date().getFullYear();
    const yearOptions = Array.from({ length: 5 }, (_, i) => currentYearNum + i + 1);

    useEffect(() => {
        const handleClickOutside = (e) => {
            if (modalRef.current && !modalRef.current.contains(e.target)) {
                onClose();
            }
        };

        if (open) {
            document.addEventListener('mousedown', handleClickOutside);
        }
        
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [open, onClose]);

    const handleMeasureSelect = (measureName) => {
        setSelectedMeasures(prev => {
            if (prev.includes(measureName)) {
                return prev.filter(name => name !== measureName);
            }
            return [...prev, measureName];
        });
    };

    const handleSelectAll = () => {
        if (selectedMeasures.length === measures.length) {
            setSelectedMeasures([]);
        } else {
            setSelectedMeasures(measures.map(measure => measure.name));
        }
    };

    const handleCopyMeasures = async () => {
        if (selectedMeasures.length === 0) {
            toast.error('Please select at least one measure to copy');
            return;
        }

        if (!targetYear) {
            toast.error('Please select a target year');
            return;
        }

        setIsLoading(true);

        try {
            await api.post(`/measures/copy-measures/`, {
                source_year: currentYear,
                target_year: targetYear,
                measure_names: selectedMeasures
            });

            toast.success(`Successfully copied ${selectedMeasures.length} measures to year ${targetYear}`);
            onClose();
        } catch (error) {
            toast.error(error.response?.data?.error || 'Failed to copy measures');
        } finally {
            setIsLoading(false);
        }
    };

    if (!open) return null;

    return (
        <div className="overlay">
            <div className="popup-content popup-user copy-measures-modal" ref={modalRef}>
                <h2>Copy Measures to Another Year</h2>
                <div className="modal-content">
                    <div className="form-control">
                        <label>Target Year</label>
                        <select
                            value={targetYear}
                            onChange={(e) => setTargetYear(e.target.value)}
                        >
                            <option value="" disabled>Select Target Year</option>
                            {yearOptions.map(year => (
                                <option key={year} value={year}>{year}</option>
                            ))}
                        </select>
                    </div>

                    <div className="form-control">
                        <label>Select Measures</label>
                        <div className="measures-list">
                            <div className="select-all">
                                <input
                                    type="checkbox"
                                    checked={selectedMeasures.length === measures.length}
                                    onChange={handleSelectAll}
                                />
                                <span>Select All</span>
                            </div>
                            {measures.map((measure) => (
                                <div key={measure.id} className="measure-item">
                                    <input
                                        type="checkbox"
                                        checked={selectedMeasures.includes(measure.name)}
                                        onChange={() => handleMeasureSelect(measure.name)}
                                    />
                                    <span>{measure.name}</span>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
                <div className="action-buttons">
                    <button className="cancel-button" onClick={onClose}>
                        Cancel
                    </button>
                    <button 
                        className="copy-button"
                        onClick={handleCopyMeasures} 
                        disabled={isLoading || selectedMeasures.length === 0 || !targetYear}
                    >
                        {isLoading ? 'Copying...' : 'Copy Measures'}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default CopyMeasuresModal;


