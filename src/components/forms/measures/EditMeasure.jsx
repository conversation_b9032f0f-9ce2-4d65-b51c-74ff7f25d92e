import React, { useState, useEffect, useRef } from 'react';
import { PrimaryButton, SecondaryButton } from '../../../components/forms/buttons';
import { TextInput, DataListInput, YearInput } from '../../../components/forms/Input';
import api, { API_URL } from '../../../api';
import toast from 'react-hot-toast';
import dayjs from 'dayjs';

const EditMeasureForm = ({ measureId, measureData, onClose, year }) => {

    const popupRef = useRef(null);

    useEffect(() => {
        const handleClickOutside = (e) => {
            if (popupRef.current && !popupRef.current.contains(e.target)) {
                onClose();
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [onClose]);
    const [formData, setFormData] = useState({
        name: '',
        synchronous_id: '',
        category_name: '',
        description: '',
        target_value: '',
        group:'',
        type: '',
        // measure_year: dayjs(),
        // startDate: '',  
        // endDate: ''     
    });
    const [categories, setCategories] = useState([]);
    const [groups, setGroups] = useState([]);
    const [types, setTypes] = useState(['numerical', 'percentage']);
    useEffect(() => {

        if (measureData) {
            setFormData({
                name: measureData.name || '',
                synchronous_id: measureData.synchronous_id || '',
                category_name: measureData.category_name || '',
                description: measureData.description || '',
                target_value: measureData.target_value || '',
                type: measureData.measure_unit || '',
                group: measureData.group || '',
                measure_year: dayjs(`${measureData.measure_year}-01-01`) || dayjs(),
                // startDate: measureData.startDate || '',  
                // endDate: measureData.endDate || '' 
            });
        }

        console.log(measureData);
        
    }, [measureData]);

    const fetchCategories = async () => {
        try {
          
            const response = await api.get(`${API_URL}/categories/list/`);
            const categoryNames = response.data.map(category => category.name);
           
            
            setCategories(categoryNames);
        } catch (error) {
            console.error('Error fetching categories:', error);
            toast.error('Failed to fetch categories');
        }
    };

    const fetchMeasureGroups = async () => {
        try {

            const response = await api.get(`${API_URL}/measures/measure-groups/`);
            const groups = response.data["measure_group_names"]
            
            setGroups(groups);
        } catch (error) {
            console.error('Error fetching categories:', error);
            toast.error('Failed to fetch categories');
        }
    };

    useEffect(() => {
        

        fetchCategories();
        fetchMeasureGroups()
    }, []);

    const handleInputChange = (name, value) => {
        setFormData({
            ...formData,
            [name]: value,
        });
    };




    const handleSubmit = async (e) => {
        e.preventDefault();
        const payload = {
            category: formData.category_name,
            name: formData.name,
            synchronous_id: formData.synchronous_id,
            description: formData.description,
            target_value: formData.target_value,
            group: formData.group,
            "measure_unit": formData.type,
            measure_year: formData.measure_year.format('YYYY'),
        };

        try {
            const response = await api.put(`${API_URL}/measures/update-measure/${measureId}/`, payload);
            if (response.status === 200) {
                toast.success('Measure updated successfully');


                const measures = JSON.parse(localStorage.getItem(`measuresOrder-${year}`)) || [];
                if (!measures.length) {

                } else {

                }
                onClose();
                window.location.reload();

            }
        } catch (error) {


            toast.error(error.response?.data?.error || 'Error updating measure');
        }
    };

    return (
        <form onSubmit={handleSubmit} ref={popupRef}>
            <div className="form-group">
                <TextInput
                    label="Measure Name"
                    name="name"
                    value={formData.name}
                    setValue={(value) => handleInputChange('name', value)}
                />
                <TextInput
                    label="Measure ID"
                    synchronous_id="synchronous_id"
                    value={formData.synchronous_id}
                    setValue={(value) => handleInputChange('synchronous_id', value)}
                />


            </div>

            <textarea
                className='text-area-measure'
                name='description'
                placeholder='Measure Descriptions'
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
            ></textarea>

            <div className="form-group">
                <DataListInput
                    label="Category"
                    name="category_name"
                    value={formData.category_name}
                    setValue={(value) => handleInputChange('category_name', value)}
                    options={categories}
                />

<DataListInput
                    label="group"
                    name="group"
                    value={formData.group}
                    setValue={(value) => handleInputChange('group', value)}
                    options={groups}
                />


            </div>
            <div className="form-group">
                <DataListInput
                    label="Type"
                    name="type"
                    value={formData.type}
                    setValue={(value) => handleInputChange('type', value)}
                    options={types}
                />

                <div onMouseDown={(e) => e.stopPropagation()} onClick={(e) => e.stopPropagation()}>
                <br></br>
                
                    <YearInput           
                        year={formData.measure_year}
                        setYear={(value) => handleInputChange('measure_year', value)}
                    />
                </div>
            </div>




            <div className="buttons">
                <SecondaryButton buttonText="Cancel" onClick={onClose} />
                <PrimaryButton type="submit" buttonText="Update Measure" />
            </div>
        </form>
    );
};

export default EditMeasureForm;
