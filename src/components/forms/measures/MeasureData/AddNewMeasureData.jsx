import { useState, useEffect, useRef } from 'react';
import { TextInput, DataListInput, DateInput } from '../../Input';
import { PrimaryButton, SecondaryButton } from '../../buttons';
import toast from 'react-hot-toast';
import { Search01Icon, TableIcon } from "hugeicons-react";
import api, { API_URL } from '../../../../api';


const AddNewMeasureData = ({ setShowNewMeasureFrom, onClose, onAddSuccess, error }) => {
    const [name, setName] = useState('');
    const [measure, setMeasure] = useState([]);
    const [hospital, setHospital] = useState([]);
    const [selectedHospital, setSelectedHospital] = useState('');
    const [value, setValue] = useState('');
    const [startingDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);
    const [dateError, setDateError] = useState('')
    const [isDateValid, setIsDateValid] = useState(false)
    const [isSaving, setIsSaving] = useState(false);
    const [measureUnit, setMeasureUnit] = useState('numerical');
    const formRef = useRef(null);
    const [hasYearData, setHasYearData] = useState(true);

    const formatDate = (dateObj) => {
        if (!dateObj) return null;

        try {
            // Handle dayjs object
            if (dateObj.$y && dateObj.$M !== undefined && dateObj.$D) {
                const year = dateObj.$y;
                const month = (dateObj.$M + 1).toString().padStart(2, '0'); // dayjs months are 0-indexed
                const day = dateObj.$D.toString().padStart(2, '0');

                console.log(`Formatting date from dayjs: ${year}-${month}-${day}`);
                return `${year}-${month}-${day}`;
            }

            // Handle Date object or string
            const date = new Date(dateObj);
            if (isNaN(date.getTime())) {
                console.error('Invalid date:', dateObj);
                return null;
            }

            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');

            console.log(`Formatting date from Date: ${year}-${month}-${day}`);
            return `${year}-${month}-${day}`;
        } catch (error) {
            console.error('Error formatting date:', error);
            return null;
        }
    };

    const popupRef = useRef(null);

    useEffect(() => {
        const handleClickOutside = (e) => {
            if (popupRef.current && !popupRef.current.contains(e.target)) {
                onClose();
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [onClose]);

    useEffect(()=>{

        const fetchMeasureUnit = async (year)=>{
            try {
                // Only make the API call if we have both a name and a year
                if (!name || !year) {
                    return;
                }

                const response = await api.get(`${API_URL}/measures/measure_unit/`,
                    { params: {
                        "measure_name": name,
                        measure_year: year
                    } }
                );

                if (response.data && response.data.type) {
                    setMeasureUnit(response.data.type);
                }
            } catch (error) {
                console.error('Error fetching measure unit:', error);
                // Don't show error toast to user as this is a background operation
            }
        }

        // Only try to access $y if startingDate exists
        if (startingDate && startingDate.$y) {
            fetchMeasureUnit(startingDate.$y);
        }

    },[name,startingDate])

    const fetchMeasures = async (year) => {
        try {

            const response = await api.get(`${API_URL}/measures/`,
                { params: { measure_year: year } }
            );
            const measuresArray = response.data.map(item => item['name']);
            setMeasure(measuresArray);
            setHasYearData(measuresArray.length > 0);
        } catch (error) {
            console.error('Error fetching measures:', error);
            toast.error('Failed to load measures');
        }
    };


    useEffect(() => {
        // Only validate if both dates are selected
        if (!startingDate || !endDate) {
            setIsDateValid(false);
            return;
        }

        try {
            // Make sure both dates have the required properties
            if (startingDate.$y === undefined || endDate.$y === undefined) {
                setIsDateValid(false);
                return;
            }

            if (startingDate.$y !== endDate.$y) {
                setIsDateValid(false);
                setDateError("Both Starting Date and Ending date should be in the same year.");
                return;
            }

            if (startingDate.$m > endDate.$m || (startingDate.$m === endDate.$m && startingDate.$d > endDate.$d)) {
                setIsDateValid(false);
                setDateError("Ending date should be greater than Starting Date.");
                return;
            }

            const year = startingDate.$y;
            fetchMeasures(year);
            setIsDateValid(true);
        } catch (error) {
            console.error('Error validating dates:', error.message);
            setIsDateValid(false);
        }

    }, [startingDate, endDate]);


    useEffect(() => {
        const fetchHospitals = async () => {
            try {

                const response = await api.get(`${API_URL}/hospitals/`
                );
                const hospitalArray = response.data.map(item => item['name']);
                setHospital(hospitalArray);
            } catch (error) {

                toast.error('Failed to load hospitals');
            }
        };
        fetchHospitals();
    }, []);


    // Function to submit the measure data to the API
    const submitMeasureData = async (measureData) => {
        setIsSaving(true);
        try {
            const response = await api.post(`${API_URL}/measures/new_measure_data/`, measureData);

            if (response.status === 201 || response.status === 200) {
                toast.success('Measure added successfully');
                // Close the popup first
                setShowNewMeasureFrom(false);
                onClose();
                // Then call the success callback if provided
                if (typeof onAddSuccess === 'function') {
                    // Use setTimeout to ensure the popup is closed before fetching data
                    setTimeout(() => {
                        onAddSuccess();
                    }, 100);
                }
            }
        } catch (error) {
            console.error('Error adding measure:', error.response?.data || error.message);
            toast.error(error.response?.data?.error || 'Failed to add measure. Please try again.');
        } finally {
            setIsSaving(false);
        }
    };



    const handleNewMeasureForm = async (e) => {
        e.preventDefault();

        if (!name || !measure.length || !selectedHospital) {
            toast.error('Please fill all fields');
            return;
        }

        const startingDateFormatted = formatDate(startingDate);
        const endingDateFormatted = formatDate(endDate);
        if (!startingDateFormatted && !endingDateFormatted) {
            toast.error('Invalid date format');
            return;
        }

        const newMeasureData = {
            measure: name,
            hospital: selectedHospital,
            value,
            starting_date: startingDateFormatted,
            end_date: endingDateFormatted
        };

        // Submit the measure data directly without checking if it exists
        submitMeasureData(newMeasureData);
    };

    return (
        <div className='form' ref={formRef}>
            <h2>New Measure Data</h2>
            <form className='new-measure-form' ref={popupRef}>
                <div className='date-range'>
                    <div className='date'>
                        <label>Starting Date</label>
                        <div
                            onMouseDown={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                        >
                            <DateInput
                                date={startingDate}
                                setDate={setStartDate}
                                placeholder="Starting date"
                                label="Starting date"
                                choices={['day', 'month', 'year']}

                            />
                        </div>
                    </div>
                    <div className='date'>
                        <label>End Date</label>
                        <div
                            onMouseDown={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}>
                            <DateInput

                                date={endDate}
                                setDate={setEndDate}
                                placeholder="End date"
                                label="End date"
                                choices={['day', 'month', 'year']}
                            />
                        </div>
                    </div>

                </div>


                {startingDate && endDate ?

                    isDateValid ?
                    hasYearData ? (
                        <>
                            <DataListInput
                                iconClass={<Search01Icon size={12}/>}
                                id="measure"
                                name="measureName"
                                placeholder="Measure name"
                                value={name}
                                setValue={setName}
                                options={measure || []}
                            />

                            <DataListInput
                                iconClass={<Search01Icon size={12} />}
                                id="hospital"
                                name="hospitalName"
                                placeholder="Hospital"
                                value={selectedHospital}
                                setValue={setSelectedHospital}
                                options={hospital || []}

                            />

                            <div className='values'>
                            <label for="value">This field only accepts numerical values do not enter text or symbols</label>
                            <div className='label-text'>

                                <TextInput
                                    type='number'
                                    name='value'
                                    placeholder='Value'
                                    value={value}
                                    setValue={setValue}
                                />

                                 <p className='symbols'>
                                 {measureUnit === "percentage"? "%":"#"}
                                 </p>
                            </div>

                                {error && <p style={{ color: 'red' }}>{error}</p>}

                            </div>

                            <div className="buttons">
                                <PrimaryButton
                                    buttonText='Save'
                                    onClick={handleNewMeasureForm}
                                    isLoading={isSaving}
                                    processingText='Saving'
                                />
                                <SecondaryButton
                                    onClick={onClose}
                                    buttonText='Cancel'
                                />
                            </div>

                        </>
                        ):(
                            <p style={{ color: 'red' }}>No data available for the selected year.</p>
                        )
                        :
                        <p className="date-error">{
                            dateError
                        }</p>

                    :
                    <p>Please select reporting period</p>
                }


            </form>

        </div>
    );
}

export default AddNewMeasureData;
