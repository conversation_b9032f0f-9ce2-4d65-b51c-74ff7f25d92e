import React, { useState, useRef, useEffect } from 'react';
import { TextInput, DataListInput, YearInput } from '../Input';
import { PrimaryButton, SecondaryButton } from '../buttons';
import toast from 'react-hot-toast';
import { Flag01Icon } from "hugeicons-react";
import api, { API_URL } from '../../../api';
import Cookies from 'js-cookie';
import dayjs from 'dayjs';
import ConfirmPopup from '../ConfirmMeasurePopup';
import moment from 'moment/moment';
import '../../../assets/css/pages/measures/popupmeasure.css';

const currentYear = dayjs();

const NewMeasureForm = ({ isOpen, onClose, onMeasureAdded }) => {
    const [name, setName] = useState('');
    const [synchronous_id, setSynchronous_id] = useState('');
    const [measure_year, setMeasureYear] = useState(currentYear);
    const [category, setCategory] = useState('');
    const [group, setGroup] = useState('');
    const [categories, setCategories] = useState([]);
    const [groups, setGroups] = useState([]);
    const [type, setType] = useState('');
    const [description, setDescription] = useState('');
    const [typeOptions] = useState(['numerical', 'percentage']);
    const [isSaving, setIsSaving] = useState(false);
    const [measureExistsMessage, setMeasureExistsMessage] = useState("");
    const [showConfirmPopup, setShowConfirmPopup] = useState(false);
    const [measureIdConfirmed, setMeasureIdConfirmed] = useState(false);
    const [isCheckingId, setIsCheckingId] = useState(false);
    const popupRef = useRef(null);

    useEffect(() => {
        const handleClickOutside = (e) => {
            if (popupRef.current && !popupRef.current.contains(e.target)) {
                onClose();
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [onClose]);

    useEffect(() => {
        if (isOpen) {
            setName('');
            setSynchronous_id('');
            setCategory('');
            setType('');
            setDescription('');
            setMeasureYear(currentYear);
            setMeasureExistsMessage("");
            setMeasureIdConfirmed(false);
        }
    }, [isOpen]);

    const fetchCategories = async () => {
        try {

            const response = await api.get(`${API_URL}/categories/list/`);
            const categoryNames = response.data.map(category => category.name);

            setCategories(categoryNames);
        } catch (error) {
            console.error('Error fetching categories:', error);
            toast.error('Failed to fetch categories');
        }
    };

    const fetchMeasureGroups = async () => {
        try {

            const response = await api.get(`${API_URL}/measures/measure-groups/`);
            const groups = response.data["measure_group_names"]

            setGroups(groups);
        } catch (error) {
            console.error('Error fetching categories:', error);
            toast.error('Failed to fetch categories');
        }
    };

    useEffect(() => {


        fetchCategories();
        fetchMeasureGroups()
    }, []);

    const checkMeasureIdExists = async (id) => {
        try {
            if (!id) return false;

            const token = Cookies.get('accessToken');
            const formattedYear = moment(measure_year).format('YYYY');

            console.log(`Checking if measure ID ${id} exists for year ${formattedYear}`);

            // Force the ID to be a string for comparison
            const idStr = id.toString();

            // For testing purposes, let's always return true to see if the popup shows
            console.log("DEBUG: Always returning true for measure ID check");
            setMeasureExistsMessage(`This Measure ID (${id}) has been used in previous reporting periods.`);
            return true;

            /* Commented out for testing
            const response = await api.get(`${API_URL}/measures/synchronous_id/`, {
                params: {
                    synchronous_id: idStr,
                    measure_year: formattedYear
                },
                headers: {
                    'Authorization': `Bearer ${token}`,
                },
            });

            console.log('API response:', response.data);

            if (response.data.is_synchronous_id_exist) {
                // Set a clear message about the ID being used in previous years
                setMeasureExistsMessage(
                    response.data.same_year_message ||
                    `This Measure ID (${id}) has been used in previous reporting periods.`
                );
                return true;
            } else {
                setMeasureExistsMessage("");
                return false;
            }
            */
        } catch (error) {
            console.error('Error checking measure ID:', error);
            toast.error('Error checking if measure ID exists');
            return false;
        }
    };

    const handleSynchronousIdChange = async (value) => {
        console.log("handleSynchronousIdChange called with value:", value);

        // Update the synchronous_id immediately
        setSynchronous_id(value);

        // Reset confirmation state
        setMeasureIdConfirmed(false);
        setMeasureExistsMessage("");
        setShowConfirmPopup(false);

        // Only check if the value is not empty and we're not already checking
        if (value && !isCheckingId) {
            try {
                // Set checking state to prevent multiple simultaneous checks
                setIsCheckingId(true);
                console.log("Starting ID check for:", value);

                // For immediate testing, let's check right away
                const exists = await checkMeasureIdExists(value);
                console.log("ID exists check result:", exists);

                if (exists) {
                    console.log("Setting showConfirmPopup to true");
                    setShowConfirmPopup(true);
                    // Force a re-render by updating state
                    setTimeout(() => {
                        console.log("Current showConfirmPopup state:", showConfirmPopup);
                    }, 100);
                } else {
                    console.log("Setting measureIdConfirmed to true");
                    setMeasureIdConfirmed(true);
                }
                setIsCheckingId(false);
            } catch (error) {
                console.error("Error in handleSynchronousIdChange:", error);
                setIsCheckingId(false);
            }
        }
    };

    const handleConfirm = () => {
        setMeasureIdConfirmed(true);
        setShowConfirmPopup(false);
    };

    const handleCancel = () => {
        // Just close the popup and clear the measure ID
        setShowConfirmPopup(false);
        setSynchronous_id("");
        setMeasureExistsMessage("");
        setMeasureIdConfirmed(false);
        // Don't close the entire form
    };

    const handleNewMeasureForm = async (event) => {
        event.preventDefault();

        if (!measureIdConfirmed && synchronous_id) {
            toast.error("Please confirm the Measure ID.");
            return;
        }

        if (!name || !category || !synchronous_id || !measure_year || !description || !type) {
            toast.error('Please fill all fields');
            return;
        }

        const transformYear = measure_year.format('YYYY');
        const newMeasure = {
            name,
            synchronous_id,
            category,
            description,
            group,
            "measure_unit":type,
            "measure_year":transformYear
        };


        setIsSaving(true);

        try {
            const token = Cookies.get('accessToken');
            const response = await api.post(`${API_URL}/measures/new_measure/`, newMeasure, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.status === 201) {
                toast.success('Measure added successfully');
                window.location.reload();
                if (typeof onMeasureAdded === 'function') {
                    onMeasureAdded(response.data);
                }

                const measures = JSON.parse(localStorage.getItem(`measuresOrder-${newMeasure.measure_year}`))
                if (measures){
                    // measures.push(response.data["measure"])
                    // localStorage.setItem(`measuresOrder-${newMeasure.measure_year}`, JSON.stringify(measures));
                }
                onClose();
                setName('');
                setSynchronous_id('');
                setCategory('');
                setType('');
                setDescription('');
                setMeasureYear(currentYear);
                setMeasureExistsMessage("");
                setMeasureIdConfirmed(false);
            } else {
                toast.error('Unexpected response. Measure may not have been saved.');
            }
        } catch (error) {
            console.error('Error adding measure:', error);
            if (error.response && error.response.data && error.response.data.error) {
                toast.error(error.response.data.error);
            } else {
                toast.error("An error occurred while saving.");
            }
        } finally {
            setIsSaving(false);
        }
    };


    // Log the current state for debugging
    console.log("Rendering NewMeasureForm with state:", {
        showConfirmPopup,
        synchronous_id,
        measureExistsMessage,
        measureIdConfirmed
    });

    return (
        <div className={`form ${isOpen ? 'open' : ''}`} ref={popupRef}>
            <h2>New Measure</h2>

            {/* Always render the popup but control visibility with CSS */}
            <div style={{ display: showConfirmPopup ? 'block' : 'none' }}>
                <ConfirmPopup
                    message={`The Measure ID "${synchronous_id}" is already in use from a previous reporting period. Would you like to continue using this ID?`}
                    onConfirm={handleConfirm}
                    onCancel={handleCancel}
                    showConfirmPopup={true} // Always true since we control visibility with CSS
                />
            </div>

            <form onSubmit={handleNewMeasureForm}>
                <TextInput
                    type='text'
                    name='measureName'
                    placeholder='Measure name'
                    value={name}
                    setValue={setName}
                />

                <div>
                    <label htmlFor="measureID">This field only accepts numerical values.</label>
                    <TextInput
                        type="number"
                        name="measureID"
                        placeholder="Measure ID"
                        value={synchronous_id}
                        setValue={handleSynchronousIdChange}
                    />
                    {measureExistsMessage && (
                        <p className="text-red">{measureExistsMessage}</p>
                    )}
                </div>

                <textarea
                    className='text-area-measure'
                    name='description'
                    placeholder='Measure Descriptions'
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                ></textarea>

                <div className='value-type'>
                <DataListInput
                    iconClass={<Flag01Icon />}
                    id="category"
                    name="categoryName"
                    placeholder="Category Name"
                    value={category}
                    setValue={setCategory}
                    options={categories}
                />

                <DataListInput
                    iconClass={<Flag01Icon />}
                    id="group"
                    name="groupName"
                    placeholder="Group Name"
                    value={group}
                    setValue={setGroup}
                    options={groups}
                />

                <DataListInput
                    id="type"
                    name="measureType"
                    placeholder="Select Type"
                    value={type}
                    options={typeOptions}
                    setValue={setType}
                />
                </div>

                <div
                    onMouseDown={(e) => e.stopPropagation()}
                    onClick={(e) => e.stopPropagation()}
                >
                    <YearInput
                        year={measure_year}
                        setYear={setMeasureYear}
                    />
                </div>

                <div className="buttons">
                    <PrimaryButton
                        buttonText="Save"
                        type="submit"
                        isLoading={isSaving}
                        processingText="Saving"
                    />
                    <SecondaryButton
                        onClick={onClose}
                        isLoading={false}
                        buttonText="Cancel"
                    />
                </div>
            </form>
        </div>
    );
};

export default NewMeasureForm;
