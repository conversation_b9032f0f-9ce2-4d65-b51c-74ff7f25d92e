import React from "react";
import {
  Delete01Icon,
  PencilEdit02Icon,
  ArrowDown01Icon,
} from "hugeicons-react";
const TargetsData = ({
  id,
  measureName,
  target,
  dateCreated,
  isChecked,
  actionIcons,
}) => {
  return (
    <div className="targets-data">
      <table className="target">
        <tbody>
          <tr>
            <td>
              <input type="checkbox" checked={isChecked} />
            </td>
            {/* <div className='input-checkbox'><td><input type='checkbox' /><ArrowDown01Icon /></td></div> */}
            <td>{id}</td>
            <td>{measureName}</td>
            <td>{target}</td>
            <td>{dateCreated}</td>
            <td>
              <div className="action-button">
                <Delete01Icon /> <PencilEdit02Icon />
              </div>
            </td>
            {/* <td className='action-button'><Delete01Icon /> | <PencilEdit02Icon /></td> */}
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default TargetsData;
