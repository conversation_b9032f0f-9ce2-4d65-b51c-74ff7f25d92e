import React, { useEffect, useState } from 'react'
import { PrimaryButton } from '../../components/forms/buttons';
import { Download04Icon, PlusSignIcon, ArrowDown01Icon } from 'hugeicons-react';
import "../../assets/css/hospitals/hospitals.css";
import HospitalPopup from './AddHospitalListPopup ';

const HospitalFilters = ({ selectedYear, onYearChange, availableYears = [] }) => {
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const handleAddNewHospitalClick = () => {
    setIsPopupVisible(true);
  };

  const handleClosePopup = () => {
    setIsPopupVisible(false);
  };

  return (
    <div className='hospital-filters'>
      <div className="hospital-year">
        <select 
          value={selectedYear} 
          onChange={(e) => onYearChange(e.target.value)}
          className="year-select"
        >
          {availableYears.length > 0 ? (
            availableYears.map(year => (
              <option key={year} value={year}>{year}</option>
            ))
          ) : (
            <option value="" disabled>No years available</option>
          )}
        </select>
        <ArrowDown01Icon 
          size={24} 
          color={"#000000"}
          variant={"stroke"}
        />
      </div>

      <PrimaryButton 
        isLoading={false}  
        processingText={'Submitting'} 
        iconClass={<PlusSignIcon />}
        onClick={handleAddNewHospitalClick} 
        buttonText={'Add New Hospital'} 
      />

      <div className="hospital-export">
        <Download04Icon 
          size={24} 
          color={"#000000"}
          variant={"stroke"}
        />
        <p>Export</p>
      </div>

      <HospitalPopup 
        isVisible={isPopupVisible} 
        onClose={handleClosePopup} 
      />
    </div>
  );
};

export default HospitalFilters;


