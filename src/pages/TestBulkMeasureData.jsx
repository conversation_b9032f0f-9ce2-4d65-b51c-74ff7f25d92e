import React, { useState } from 'react';
import BulkAddMeasureData from '../components/forms/measures/MeasureData/BulkAddMeasureData';
import { PrimaryButton } from '../components/forms/buttons';

const TestBulkMeasureData = () => {
    const [showBulkForm, setShowBulkForm] = useState(false);

    const handleAddSuccess = () => {
        console.log('Bulk add successful!');
        // In a real implementation, this would refresh the data
    };

    return (
        <div style={{ padding: '50px', maxWidth: '800px', margin: '0 auto' }}>
            <h1>Test Bulk Measure Data</h1>
            <p>Click the button below to test the bulk measure data entry form:</p>
            
            <PrimaryButton 
                buttonText="Open Bulk Add Form" 
                onClick={() => setShowBulkForm(true)} 
            />
            
            {showBulkForm && (
                <BulkAddMeasureData
                    onClose={() => setShowBulkForm(false)}
                    onAddSuccess={handleAddSuccess}
                />
            )}
        </div>
    );
};

export default TestBulkMeasureData;
