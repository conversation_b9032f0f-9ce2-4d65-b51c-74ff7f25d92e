import React, { useState } from 'react';
import '../assets/css/pages/measures/popupmeasure.css';

const TestPopupPage = () => {
    const [showPopup, setShowPopup] = useState(false);
    
    const handleConfirm = () => {
        console.log('Confirmed!');
        setShowPopup(false);
    };
    
    const handleCancel = () => {
        console.log('Cancelled!');
        setShowPopup(false);
    };
    
    return (
        <div style={{ padding: '50px', maxWidth: '800px', margin: '0 auto' }}>
            <h1>Test Popup Page</h1>
            <p>Click the button below to test the confirmation popup:</p>
            
            <button 
                onClick={() => setShowPopup(true)}
                style={{
                    padding: '10px 20px',
                    backgroundColor: '#4CAF50',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '16px'
                }}
            >
                Show Confirmation Popup
            </button>
            
            {showPopup && (
                <div className="confirm-popup show">
                    <div className="popup-content">
                        <h3>Confirm Measure ID</h3>
                        <p>The Measure ID "123" is already in use from a previous reporting period. Would you like to continue using this ID?</p>
                        <div className="popup-buttons">
                            <button 
                                onClick={handleCancel}
                                style={{
                                    padding: '8px 16px',
                                    backgroundColor: '#f44336',
                                    color: 'white',
                                    border: 'none',
                                    borderRadius: '4px',
                                    cursor: 'pointer',
                                    marginRight: '10px'
                                }}
                            >
                                Cancel
                            </button>
                            <button 
                                onClick={handleConfirm}
                                style={{
                                    padding: '8px 16px',
                                    backgroundColor: '#4CAF50',
                                    color: 'white',
                                    border: 'none',
                                    borderRadius: '4px',
                                    cursor: 'pointer'
                                }}
                            >
                                Yes, Use This ID
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default TestPopupPage;
