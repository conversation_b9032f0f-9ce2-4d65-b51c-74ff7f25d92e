import React, { useState } from 'react';
import { PrimaryButton } from '../components/forms/buttons';
import Dashboard from './dashboard/Dashboard';

const TestYTDDashboard = () => {
    const [showDashboard, setShowDashboard] = useState(false);

    return (
        <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
            <h1>Test YTD Dashboard Feature</h1>
            
            <div style={{ 
                background: '#f5f5f5', 
                padding: '20px', 
                borderRadius: '8px', 
                marginBottom: '20px' 
            }}>
                <h2>YTD (Year-to-Date) Feature</h2>
                <p>This feature has been implemented with the following capabilities:</p>
                
                <ul style={{ marginLeft: '20px', marginTop: '10px' }}>
                    <li><strong>Default Selection:</strong> YTD is now the default option in the month dropdown</li>
                    <li><strong>Data Aggregation:</strong> Automatically sums data from January to the current month</li>
                    <li><strong>Total Cards:</strong> Shows aggregated totals for Acute, Swing Bed, Observation, and Emergency Room</li>
                    <li><strong>Emergency Room Data:</strong> Displays cumulative emergency room utilization across all hospitals</li>
                    <li><strong>Performance Measures:</strong> Shows performance measures data for the year-to-date period</li>
                    <li><strong>Visual Indicator:</strong> Clear indication when YTD mode is active</li>
                    <li><strong>Loading States:</strong> Shows loading indicators during YTD data aggregation</li>
                </ul>

                <div style={{ 
                    background: '#e3f2fd', 
                    border: '1px solid #2196f3', 
                    borderRadius: '4px', 
                    padding: '10px', 
                    marginTop: '15px' 
                }}>
                    <strong>How it works:</strong> When YTD is selected, the system makes multiple API calls to fetch data 
                    for each month from January to the current month, then aggregates the results to show cumulative totals.
                </div>
            </div>

            <div style={{ marginBottom: '20px' }}>
                <PrimaryButton 
                    buttonText={showDashboard ? "Hide Dashboard" : "Show Dashboard with YTD"} 
                    onClick={() => setShowDashboard(!showDashboard)} 
                />
            </div>

            {showDashboard && (
                <div style={{ 
                    border: '2px solid #ddd', 
                    borderRadius: '8px', 
                    overflow: 'hidden' 
                }}>
                    <Dashboard />
                </div>
            )}

            <div style={{ 
                marginTop: '20px', 
                padding: '15px', 
                background: '#fff3cd', 
                border: '1px solid #ffeaa7', 
                borderRadius: '4px' 
            }}>
                <strong>Note:</strong> The YTD feature aggregates data on the frontend by making multiple API calls. 
                For optimal performance in production, consider implementing YTD aggregation on the backend.
            </div>
        </div>
    );
};

export default TestYTDDashboard;
