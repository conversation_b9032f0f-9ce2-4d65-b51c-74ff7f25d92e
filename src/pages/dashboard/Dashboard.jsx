import React from "react";
import "../../assets/css/dashboard/dashboard.css";
import { FileExportIcon, InformationCircleIcon } from "hugeicons-react";
import { useEffect, useState } from "react";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import { LineChart } from "@mui/x-charts/LineChart";
import jsPDF from "jspdf";
import "jspdf-autotable";
import ErrorComponent from '../../components/ErrorComponent/ErrorPage'
import { Timer02Icon, BedIcon, SearchingIcon, WorkoutRunIcon, } from "hugeicons-react";
import DashboardTotalCard from "../../components/dashboard/DashboardTotalCard";
import DashboardContainer from "../../components/dashboard/DashboardContainer";
import api, { API_URL } from "../../api";
import TableLoading from "../../components/loading/TableLoading";

const monthToNumber = {
  January: 1, February: 2, March: 3, April: 4, May: 5, June: 6,
  July: 7, August: 8, September: 9, October: 10, November: 11, December: 12,
  YTD: 'ytd'
};

const DashboardContent = () => {
  const [currentDateYear, setCurrentDateYear] = useState(
    new Date().getFullYear()
  );
  const [filterDurationYear, setFilterDurationYear] = useState(
    new Date().getFullYear()
  );

  const [prevYear, setPrevYear] = useState(new Date().getFullYear() - 1);
  const [prevYearMinusTwo, setPrevYearMinusTwo] = useState(
    new Date().getFullYear() - 2
  );
  const [prevYearMinusThree, setPrevYearMinusThree] = useState(
    new Date().getFullYear() - 3
  );
  const [filterDurationMonth, setFilterDurationMonth] = useState(
    new Date().getMonth()
  );

  const [measuresDataYear, setMeasuresDataYear] = useState("this_year")

  const [measureBy, setMeasureBy] = useState("ED Mortality Rate");
  const [selectedMonth, setSelectedMonth] = useState("YTD");
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [tableData, setTableData] = useState([]);
  const [chatData, setChatData] = useState([]);
  const [fetchingPerformanceMeasures, setFetchingPerformanceMeasures] = useState(true);
  const [isLoadingYTD, setIsLoadingYTD] = useState(false);

  const [totalData, setTotalData] = useState({});
  const [emmergencyData, setEmmergencyData] = useState({});
  const [maxEmmergencyData, setMaxEmmergencyData] = useState(0);
  const [measuresList, setMeasuresList] = useState('')
  const [error, setError] = useState(null);



  const categories = [
    "CENSUS - VOLUME & UTILIZATION",
    "CLINICAL QUALITY",
    "HUMAN RESOURCES"
  ];

  const fetchPerformanceMeasures = async (value) => {
    setMeasuresList([]);
    try {
      setFetchingPerformanceMeasures(true);

      if (selectedMonth === 'YTD') {
        // For YTD, we'll use the current month as the endpoint
        // The backend should ideally handle YTD aggregation, but for now we'll use current month
        const currentMonth = new Date().getMonth() + 1;
        const response = await api.get(
          `${API_URL}/dashboard/measures_table_data/${value || selectedYear}/${currentMonth}/`
        );

        if (response.status === 200) {
          const data = response.data;

          const uniqueMeasures = new Set();
          for (let measure in data) {
            data[measure].forEach(item => {
              uniqueMeasures.add(item["name"]);
            });
          }

          setMeasureBy(uniqueMeasures.values().next().value);
          setMeasuresList([...uniqueMeasures]);

          const groupedData = categories.reduce((acc, category) => {
            acc[category] = response.data.measures.filter(el => el.category === category);
            return acc;
          }, {});
          setTableData(groupedData);
        }
      } else {
        // Regular month selection
        const monthParam = monthToNumber[selectedMonth] || 1;
        const response = await api.get(
          `${API_URL}/dashboard/measures_table_data/${value || selectedYear}/${monthParam}/`
        );

        if (response.status === 200) {
          const data = response.data;

          const uniqueMeasures = new Set();
          for (let measure in data) {
            data[measure].forEach(item => {
              uniqueMeasures.add(item["name"]);
            });
          }

          setMeasureBy(uniqueMeasures.values().next().value);
          setMeasuresList([...uniqueMeasures]);

          const groupedData = categories.reduce((acc, category) => {
            acc[category] = response.data.measures.filter(el => el.category === category);
            return acc;
          }, {});
          setTableData(groupedData);
        }
      }

      setFetchingPerformanceMeasures(false);
    } catch (error) {
      console.log(error);
      setFetchingPerformanceMeasures(false);
    }
  };

  const fetchTotalsData = async (value) => {
    try {
      if (selectedMonth === 'YTD') {
        setIsLoadingYTD(true);
        const currentYear = new Date().getFullYear();
        const currentMonth = new Date().getMonth() + 1; // Current month (1-12)

        // Determine the end month based on the selected year
        const endMonth = (Number(value) === currentYear) ? currentMonth : 12;

        let aggregatedData = {
          total_acute: 0,
          total_swing_bed: 0,
          total_observation: 0,
          total_emergency_room: 0
        };

        // Fetch data for each month from January to end month
        for (let month = 1; month <= endMonth; month++) {
          try {
            const response = await api.get(`${API_URL}/dashboard/dashboard_totals/${value}/${month}/`);
            if (response.status === 200 && response.data[Number(value)]) {
              const monthData = response.data[Number(value)];
              aggregatedData.total_acute += monthData.total_acute || 0;
              aggregatedData.total_swing_bed += monthData.total_swing_bed || 0;
              aggregatedData.total_observation += monthData.total_observation || 0;
              aggregatedData.total_emergency_room += monthData.total_emergency_room || 0;
            }
          } catch (monthError) {
            console.log(`Error fetching data for month ${month}:`, monthError);
          }
        }
        setTotalData(aggregatedData);
        setIsLoadingYTD(false);
      } else {
        // Regular month selection
        const monthParam = monthToNumber[selectedMonth] || 1;
        const response = await api.get(`${API_URL}/dashboard/dashboard_totals/${value}/${monthParam}/`);
        if (response.status === 200) {
          setTotalData(response.data[Number(value)]);
        }
      }
    } catch (error) {
      console.log(error);
      setIsLoadingYTD(false);
    }
  };

  const fetchMeasuresChartData = async (selected_year, measure_by) => {

    try {
      const response = await api.get(
        `${API_URL}/dashboard/measures_chart_data/${selected_year}/${measure_by || "ED Mortality Rate"}/`
      );

      if (response.status === 200) {

        setChatData(
          response.data
            .map((data) =>
              data.measures
                .map((measure) =>
                  measure.hospitals.map((hospital) => ({
                    label: hospital.name,
                    data: hospital.months.map(
                      (monthData) => Object.values(monthData)[0]
                    ),
                  }))
                )
                .flat()
            )
            .flat()
        );
      }
    } catch (error) {
      console.log(error);
    }
  };

  const fetchEmergencyData = async (year, month) => {
    try {
      const yearToUse = year || selectedYear;
      const monthToUse = month || selectedMonth;

      console.log("Fetching emergency data for:", { yearToUse, monthToUse });

      if (monthToUse === 'YTD') {
        // For YTD, aggregate data from January to end month
        const currentYear = new Date().getFullYear();
        const currentMonth = new Date().getMonth() + 1;
        const endMonth = (Number(yearToUse) === currentYear) ? currentMonth : 12;

        let aggregatedData = {
          Carnegie: 0,
          Pawhuska: 0,
          Seiling: 0,
          Prague: 0,
          Mangum: 0
        };

        // Fetch data for each month from January to end month
        for (let monthNum = 1; monthNum <= endMonth; monthNum++) {
          try {
            const response = await api.get(
              `${API_URL}/dashboard/emergency_rooms_data/${yearToUse}/${monthNum}/`
            );

            if (response.status === 200 && response.data[yearToUse]?.[monthNum]) {
              const monthData = response.data[yearToUse][monthNum];
              aggregatedData.Carnegie += Number(monthData.Carnegie) || 0;
              aggregatedData.Pawhuska += Number(monthData.Pawhuska) || 0;
              aggregatedData.Seiling += Number(monthData.Seiling) || 0;
              aggregatedData.Prague += Number(monthData.Prague) || 0;
              aggregatedData.Mangum += Number(monthData.Mangum) || 0;
            }
          } catch (monthError) {
            console.log(`Error fetching emergency data for month ${monthNum}:`, monthError);
          }
        }

        setEmmergencyData(aggregatedData);

        // Calculate total
        const total = Object.values(aggregatedData).reduce((acc, val) => acc + val, 0);
        setMaxEmmergencyData(total);

      } else {
        // Regular month selection
        const monthNumber = monthToNumber[monthToUse] || 1;
        const response = await api.get(
          `${API_URL}/dashboard/emergency_rooms_data/${yearToUse}/${monthNumber}/`
        );

        if (response.status === 200) {
          console.log("Emergency data response:", response.data);

          // Check if data exists for the specified year and month
          if (!response.data[yearToUse]?.[monthNumber]) {
            console.log("No emergency data found for year/month:", yearToUse, monthNumber);
            setEmmergencyData({});
            setMaxEmmergencyData(0);
          } else {
            const monthData = response.data[yearToUse][monthNumber];
            console.log("Month data:", monthData);

            setEmmergencyData(monthData);

            // Calculate total, ensuring all values are numbers
            const values = [
              monthData['Carnegie'] || 0,
              monthData['Pawhuska'] || 0,
              monthData['Seiling'] || 0,
              monthData['Prague'] || 0,
              monthData['Mangum'] || 0,
            ].map(val => Number(val) || 0);

            const total = values.reduce((accumulator, currentValue) => accumulator + currentValue, 0);
            setMaxEmmergencyData(total);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching emergency data:", error);
      setEmmergencyData({});
      setMaxEmmergencyData(0);
    }
  };

  useEffect(() => {
    console.log("Dashboard useEffect triggered with:", { selectedYear, selectedMonth });
    fetchTotalsData(selectedYear);
    fetchEmergencyData(selectedYear, selectedMonth);
    fetchPerformanceMeasures();
    fetchMeasuresChartData(selectedYear, measureBy);
  }, [selectedYear, selectedMonth]);

  const handleDurationYearChange = (value) => {
    handleFilter(value, filterDurationMonth, measureBy);
    setFilterDurationYear(value);
    setSelectedYear(value)
  };

  const handleMeasureBy = (value) => {

    setMeasureBy(value);

  };

  useEffect(() => {
    fetchMeasuresChartData(filterDurationYear, measureBy);

  }, [measureBy]);

  const handleSelectedMonth = (value) => {
    setSelectedMonth(value);
  };

  const handleSelectedYear = (value) => {

    if (value === "this_year") {
      const date = new Date().getFullYear()
      setSelectedYear(date);
      setMeasuresDataYear("this_year")

    } else {
      const date = new Date().getFullYear() - 1

      setSelectedYear(date);
      setMeasuresDataYear("last_year")
    }

  };

  const xLabels = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  const handleFilter = (filterDurationYear, filterDurationMonth, measure) => {

    fetchTotalsData(filterDurationYear);
    fetchPerformanceMeasures(filterDurationYear);
    fetchEmergencyData(filterDurationYear);
  };
  const handleExport = () => {
    if (!tableData || typeof tableData !== 'object' || Array.isArray(tableData)) {
      return;
    }

    const doc = new jsPDF();
    let startY = 10;

    doc.setFontSize(12);
    doc.text("Performance Measures Report", 105, startY, { align: "center" });
    startY += 5;

    Object.keys(tableData).forEach(category => {
      if (!tableData[category] || tableData[category].length === 0) {
        console.warn(`No data for category: ${category}`);
        return;
      }

      doc.setFontSize(14);
      doc.text(category, 10, startY);
      startY += 5;

      const rows = tableData[category].map(measure => {
        return {
          Measure: measure.name || "-",
          Goal: measure.measure_unit === "percentage" && measure.BM !== undefined
            ? `${measure.BM}%`
            : measure.BM || "-",
          ...Object.fromEntries(
            (measure.hospitals || []).map(hospital => [
              hospital.name,
              hospital.value !== undefined && measure.measure_unit === "percentage"
                ? `${hospital.value}%`
                : hospital.value !== undefined
                  ? hospital.value
                  : "-"
            ])
          )
        };
      });

      if (rows.length === 0) {
        console.warn(`No measures found for category: ${category}`);
        return;
      }

      const headers = [
        { header: "Measure", dataKey: "Measure" },
        { header: "Goal", dataKey: "Goal" },
        ...(tableData[category][0]?.hospitals || []).map(hospital => ({
          header: hospital.name,
          dataKey: hospital.name,
        })),
      ];

      doc.autoTable({
        startY,
        head: [headers.map(header => header.header)],
        body: rows.map(row => headers.map(header => row[header.dataKey] || "-")),
        theme: "grid",
      });

      startY = doc.lastAutoTable.finalY + 5;
    });

    doc.save("performance_measures.pdf");
  };

  const getColor = (index) => {
    const colors = [
      "#FF5733",
      "#33FF57",
      "#3357FF",
      "#FF33A1",
      "#F0A500",
    ];
    return colors[index % colors.length];
  };



  return (
    <div className="dashboard-container">

      <div className="top-actions">
        <div className="export-icon export-bttn" onClick={handleExport}>
          <FileExportIcon size={24} />
          <p className="">Export Table</p>
        </div>
        <div className="filter-container">
          <Select className="filter-year-select"
            onChange={(e) => {
              handleDurationYearChange(e.target.value);
            }}
            name="duration"
            id="duration"
            value={selectedYear}
          >
            <MenuItem value={currentDateYear}>{currentDateYear}</MenuItem>
            <MenuItem value={prevYear}>{prevYear}</MenuItem>
            <MenuItem value={prevYearMinusTwo}>{prevYearMinusTwo}</MenuItem>
            <MenuItem value={prevYearMinusThree}>{prevYearMinusThree}</MenuItem>
          </Select>
          <Select
            onChange={(e) => {
              handleSelectedMonth(e.target.value);
            }}
            name="selectedMonth"
            id="selectedMonth"
            value={selectedMonth}
          >
            <MenuItem value="YTD">YTD (Year-to-Date)</MenuItem>
            <MenuItem value="January">January</MenuItem>
            <MenuItem value="February">February</MenuItem>
            <MenuItem value="March">March</MenuItem>
            <MenuItem value="April">April</MenuItem>
            <MenuItem value="May">May</MenuItem>
            <MenuItem value="June">June</MenuItem>
            <MenuItem value="July">July</MenuItem>
            <MenuItem value="August">August</MenuItem>
            <MenuItem value="September">September</MenuItem>
            <MenuItem value="October">October</MenuItem>
            <MenuItem value="November">November</MenuItem>
            <MenuItem value="December">December</MenuItem>
          </Select>
        </div>
      </div>

      {selectedMonth === 'YTD' && (
        <div className="ytd-indicator">
          {isLoadingYTD ? (
            <>⏳ Loading Year-to-Date data...</>
          ) : (
            <>
              Displaying Year-to-Date data (January - {
                Number(selectedYear) === new Date().getFullYear()
                  ? new Date().toLocaleString('default', { month: 'long' })
                  : 'December'
              })
            </>
          )}
        </div>
      )}
      {fetchingPerformanceMeasures ? (
        <div className="table-container">
          <TableLoading />
        </div>
      ) : (

        <>
          <div className="total-hospital-cards">
            <DashboardTotalCard
              className="total-card"
              icon={
                <Timer02Icon size={24} color={"#07AEEF"} variant={"stroke"} />
              }
              value={
                totalData.total_acute ? (
                  <h2>{totalData.total_acute}</h2>
                ) : (
                  <span>No Available Data</span>
                )
              }
              name={"Total Acute"}
            />
            <DashboardTotalCard
              className="total-card"
              icon={<BedIcon size={24} color={"#FFB60A"} variant={"stroke"} />}
              value={
                totalData.total_swing_bed ? (
                  <h2>{totalData.total_swing_bed}</h2>
                ) : (
                  <span>No Available Data</span>
                )
              }
              name={"Total Swingbed"}
            />
            <DashboardTotalCard
              className="total-card"
              icon={
                <SearchingIcon size={24} color={"#FC7D75"} variant={"stroke"} />
              }
              value={
                totalData.total_observation ? (
                  <h2>{totalData.total_observation}</h2>
                ) : (
                  <span>No Available Data</span>
                )
              }
              name={"Total Observation"}
            />
            <DashboardTotalCard
              className="total-card"
              icon={
                <WorkoutRunIcon
                  size={24}
                  color={"#0E76BC"}
                  variant={"stroke"}
                />
              }
              value={
                totalData.total_emergency_room ? (
                  <h2>{totalData.total_emergency_room}</h2>
                ) : (
                  <span>No Available Data</span>
                )
              }
              name={"Total Emergency Room"}
            />
          </div>
          <div className="chart-row">
            <div className="chart-container">
              <div className="row">
                <h4>Measure By: </h4>

                <Select
                  onChange={(e) => {
                    handleMeasureBy(e.target.value);
                  }}
                  name="measureBy"
                  id="measureBy"
                  value={measureBy}
                >
                  {
                    measuresList.map((measure, index) => (

                      <MenuItem key={index} value={measure} >{measure}
                      </MenuItem>

                    ))
                  }

                </Select>
              </div>

              <LineChart
                height={300}
                className="chart"
                series={chatData.map((data, index) => ({
                  data: data.data,
                  label: data.label,
                  color: getColor(index),
                }))}
                xAxis={[{ scaleType: "point", data: xLabels }]}
              />


            </div>
            <div className="progress-container">
              <div className="row">
                <h4>Emergency Room Utilization</h4>
                {/* {process.env.NODE_ENV === 'development' && (
                  <div style={{ fontSize: '12px', color: '#666', marginBottom: '10px' }}>
                    Debug: maxEmmergencyData = {maxEmmergencyData},
                    emmergencyData = {JSON.stringify(emmergencyData)}
                  </div>
                )} */}
                {maxEmmergencyData !== 0 ?

                  <>
                    <p className="description">
                      Percentage of total ER visits across all hospitals. Each percentage represents the proportion of ER visits for each hospital, with the sum totaling 100%.
                    </p>
                    <div className="hospital-progress">
                      <div className="row">
                        <span className="name">Carnegie</span>
                        <progress
                          value={
                            emmergencyData.Carnegie && maxEmmergencyData > 0
                              ? ((Number(emmergencyData.Carnegie) / maxEmmergencyData) * 100).toFixed(2)
                              : 0
                          }
                          max={100}
                        />
                        <span>
                          {emmergencyData.Carnegie && maxEmmergencyData > 0
                            ? ((Number(emmergencyData.Carnegie) / maxEmmergencyData) * 100).toFixed(2)
                            : 0}
                          %
                        </span>
                      </div>
                      <div className="row">
                        <span className="name">Mangum</span>
                        <progress
                          value={
                            emmergencyData.Mangum && maxEmmergencyData > 0
                              ? ((Number(emmergencyData.Mangum) / maxEmmergencyData) * 100).toFixed(2)
                              : 0
                          }
                          max={100}
                        />
                        <span>
                          {emmergencyData.Mangum && maxEmmergencyData > 0
                            ? ((Number(emmergencyData.Mangum) / maxEmmergencyData) * 100).toFixed(2)
                            : 0}
                          %
                        </span>
                      </div>
                      <div className="row">
                        <span className="name">Pawhuska</span>
                        <progress
                          value={
                            emmergencyData.Pawhuska && maxEmmergencyData > 0
                              ? ((Number(emmergencyData.Pawhuska) / maxEmmergencyData) * 100).toFixed(2)
                              : 0
                          }
                          max={100}
                        />
                        <span>
                          {emmergencyData.Pawhuska && maxEmmergencyData > 0
                            ? ((Number(emmergencyData.Pawhuska) / maxEmmergencyData) * 100).toFixed(2)
                            : 0}
                          %
                        </span>
                      </div>
                      <div className="row">
                        <span className="name">Prague</span>
                        <progress
                          value={
                            emmergencyData.Prague && maxEmmergencyData > 0
                              ? ((Number(emmergencyData.Prague) / maxEmmergencyData) * 100).toFixed(2)
                              : 0
                          }
                          max={100}
                        />
                        <span>
                          {emmergencyData.Prague && maxEmmergencyData > 0
                            ? ((Number(emmergencyData.Prague) / maxEmmergencyData) * 100).toFixed(2)
                            : 0}
                          %
                        </span>
                      </div>
                      <div className="row">
                        <span className="name">Seiling</span>
                        <progress
                          value={
                            emmergencyData.Seiling && maxEmmergencyData > 0
                              ? ((Number(emmergencyData.Seiling) / maxEmmergencyData) * 100).toFixed(2)
                              : 0
                          }
                          max={100}
                        />
                        <span>
                          {emmergencyData.Seiling && maxEmmergencyData > 0
                            ? ((Number(emmergencyData.Seiling) / maxEmmergencyData) * 100).toFixed(2)
                            : 0}
                          %
                        </span>
                      </div>
                    </div>
                  </>
                  :

                  <p>No Data Available</p>

                }

              </div>

            </div>
          </div>
          <div className="table-container">
            <div className="row">
              <h3>Performance Measures</h3>
            </div>
            <table>
              <thead>
                <tr>
                  <th>Measures</th>
                  <th className="bm">Goal</th>
                  <th>Carnegie</th>
                  <th>Mangum</th>
                  <th>Pawhuska</th>
                  <th>Prague</th>
                  <th>Seiling</th>
                </tr>
              </thead>
              <tbody>
                {categories.map((category) => (
                  <React.Fragment key={category}>
                    <tr>
                      <td colSpan={7} style={{ fontWeight: 'bold' }}>{category}</td>
                    </tr>
                    {tableData[category].map((el, rowIndex) => (
                      <tr key={rowIndex}>
                        <td className="hover-container">
                          {el.name}
                          <div className="hover-info">
                            <InformationCircleIcon color="#CCCCCC" />

                            <div className="hover-description">
                              {el.measure_description}
                            </div>
                          </div>
                        </td>

                        <td className="bm">{el.BM !== null ? el.BM : "-"} {el.measure_unit === "Percentage" && (el.BM || el.BM == 0) ? "%" : ""}</td>
                        {el.hospitals && el.hospitals.length > 0 ? (
                          el.hospitals.map((hospital, colIndex) => (
                            <td
                              key={hospital.name}
                              className={
                                (el.BM === null) ? "" :
                                  hospital.value < el.BM && el.value_condition ==="Lower is better"
                                    ? "green-bg"
                                    : (hospital.value > el.BM && el.value_condition === "Lower is better")
                                      ? "red-bg"
                                      : (el.BM === null) ? ""
                                        : "yellow-bg"
                              }
                            >
                              {hospital.value !== undefined ? hospital.value : '-'}{el.measure_unit === "Percentage" && hospital.value ? "%" : ""}
                            </td>
                          ))
                        ) : (
                          <td colSpan={5}>No hospital data available</td>
                        )}
                      </tr>
                    ))}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
          {error && (
            <ErrorComponent
            // message={error}
            // onRetry={fetchPerformanceMeasures}
            // severity="error"
            />
          )}
        </>
      )}
    </div>
  );
};

const Dashboard = () => {
  return (
    <DashboardContainer content={<DashboardContent />} pageTitle={"Overview"} />
  );
};
export default Dashboard;